import { Injectable } from '@angular/core';
import { PrintTemplateUtil } from '../utils/print-template.util';
import { CartCalculationUtils } from '../utils/cart-calculation.utils';
import { PrintTemplateData, PrintableItem } from '../models/print.model';
import { Order, CartItem } from '../models';
import { TypeSenseService } from './typesense';

@Injectable({
  providedIn: 'root'
})
export class PrintService {

  constructor(private typeSenseService: TypeSenseService) { }

  /**
   * Get store information with fallback to default values
   */
  private async getStoreInfo(): Promise<{address: string, phone: string, gstin: string, name: string, email: string}> {
    try {
      const storeData = await this.typeSenseService.getStoreById();
      return {
        address: storeData?.address || 'New Delhi, India',
        phone: storeData?.phone || '9667018020',
        gstin: storeData?.gstin || '27AAPC1234D1Z1',
        name: storeData?.name || 'ROZANA RURAL COMMERCE PVT LTD',
        email: storeData?.email || '<EMAIL>'
      };
    } catch (error) {
      console.error('Error fetching store info, using defaults:', error);
      return {
        address: 'New Delhi, India',
        phone: '9667018020',
        gstin:'27AAPC1234D1Z1',
        name: 'ROZANA RURAL COMMERCE PVT LTD',
        email: '<EMAIL>' 
      };
    }
  }

  /**
   * Convert CartItem to PrintableItem
   */
  private convertCartItemToPrintableItem(cartItem: CartItem): PrintableItem {
    const mrp = cartItem.mrp || cartItem.selling_price;

    // Since selling_price is inclusive of tax, calculate taxable amount first
    const totalTaxRate = (cartItem.igst || 0) + (cartItem.cess || 0);
    const itemTaxableAmount = totalTaxRate > 0
      ? (cartItem.selling_price * cartItem.quantity) / (1 + (totalTaxRate / 100))
      : cartItem.selling_price * cartItem.quantity;

    // Calculate tax amounts based on taxable amount
    const igstAmount = cartItem.igst ? (itemTaxableAmount * cartItem.igst) / 100 : 0;
    const cessAmount = cartItem.cess ? (itemTaxableAmount * cartItem.cess) / 100 : 0;
    const cgstAmount = igstAmount > 0 ? igstAmount / 2 : 0;
    const sgstAmount = igstAmount > 0 ? igstAmount / 2 : 0;
    const totalTax = igstAmount + cessAmount;

    return {
      sku: cartItem.child_sku,
      name: cartItem.name,
      quantity: cartItem.quantity,
      unit_price: mrp,
      sale_price: cartItem.selling_price,
      mrp,
      discount: Math.max(0, mrp - cartItem.selling_price),
      total: cartItem.selling_price * cartItem.quantity,
      cgst: cartItem.cgst,
      sgst: cartItem.sgst,
      igst: cartItem.igst,
      cess: cartItem.cess,
      tax: cartItem.tax,
      igstAmount,
      cessAmount,
      cgstAmount,
      sgstAmount,
      totalTax,
      totalWithTax: itemTaxableAmount + totalTax
    };
  }

  /**
   * Convert Order to PrintableItem array
   */
  private convertOrderItemsToPrintableItems(order: Order): PrintableItem[] {
    return order.items.map(orderItem => {
      // For orders, we need to extract tax information from the order item
      // If tax rates are not available, we'll calculate based on total tax
      const totalTaxRate = orderItem.tax || 0;
      const igstRate = totalTaxRate; // Assuming all tax is IGST for simplicity
      const cessRate = 0; // CESS would need to be stored separately

      // Since sale_price is inclusive of tax, calculate taxable amount first
      const itemTaxableAmount = totalTaxRate > 0
        ? (orderItem.sale_price * orderItem.quantity) / (1 + (totalTaxRate / 100))
        : orderItem.sale_price * orderItem.quantity;

      const igstAmount = (itemTaxableAmount * igstRate) / 100;
      const cessAmount = (itemTaxableAmount * cessRate) / 100;
      const cgstAmount = igstAmount > 0 ? igstAmount / 2 : 0;
      const sgstAmount = igstAmount > 0 ? igstAmount / 2 : 0;
      const totalTax = igstAmount + cessAmount;

      return {
        sku: orderItem.sku,
        name: orderItem.name,
        quantity: orderItem.quantity,
        unit_price: orderItem.unit_price,
        sale_price: orderItem.sale_price,
        mrp: orderItem.unit_price,
        discount: Math.max(0, orderItem.unit_price - orderItem.sale_price),
        total: orderItem.sale_price * orderItem.quantity,
        cgst: 0,
        sgst: 0,
        igst: igstRate,
        cess: cessRate,
        tax: orderItem.tax || 0,
        igstAmount,
        cessAmount,
        cgstAmount,
        sgstAmount,
        totalTax,
        totalWithTax: itemTaxableAmount + totalTax
      };
    });
  }

  /**
   * Generate print template data
   */
  private async generateTemplateData(
    items: PrintableItem[],
    orderId: string,
    customerName: string,
    paymentMethod: string,
    copyOfInvoice: boolean,
    orderDate?: Date,
    totalAmount?: number,
    subtotalAmount?: number,
    discountAmount?: number,
    facilityName?: string
  ): Promise<PrintTemplateData> {
    const totals = CartCalculationUtils.calculatePrintableTotals(items);
    const calculatedTotal = totals.totalTaxableAmount + totals.totalTax;
    const calculatedDiscount = items.reduce((total, item) => total + (item.discount || 0) * item.quantity, 0);
    const storeInfo = await this.getStoreInfo();

    return {
      order_id: orderId,
      customer_name: customerName,
      customer_id: '',
      facility_name: facilityName || storeInfo.name,
      total_amount: totalAmount || calculatedTotal,
      items,
      payment_method: paymentMethod,
      subtotal: subtotalAmount || totals.totalTaxableAmount,
      discount: discountAmount ?? calculatedDiscount,
      grand_total: totalAmount || calculatedTotal,
      copy_of_invoice: copyOfInvoice,
      currentDate: (orderDate || new Date()).toLocaleDateString('en-IN'),
      totals,
      storeAddress: storeInfo.address,
      storePhone: storeInfo.phone,
      storeGSTIN: storeInfo.gstin,
      storeEmail: storeInfo.email
    };
  }

  /**
   * Generate print template for cart items
   */
  async generateCartPrintTemplate(
    cartItems: CartItem[],
    orderId: string,
    customerName?: string,
    paymentMethod: string = 'Cash',
    copyOfInvoice: boolean = false
  ): Promise<string> {
    const printableItems = cartItems.map(item => this.convertCartItemToPrintableItem(item));
    const templateData = await this.generateTemplateData(
      printableItems,
      orderId,
      customerName || '',
      paymentMethod,
      copyOfInvoice
    );
    return PrintTemplateUtil.generateTemplate(templateData);
  }

  /**
   * Generate print template for an order
   */
  async generateOrderPrintTemplate(order: Order, copyOfInvoice: boolean = false): Promise<string> {
    const printableItems = this.convertOrderItemsToPrintableItems(order);
    const templateData = await this.generateTemplateData(
      printableItems,
      order.order_id,
      order.customer_name,
      order.payment?.payment_mode || 'Cash',
      copyOfInvoice,
      new Date(order.order_date),
      order.total_amount,
      order.subtotal_amount,
      order.discount_amount,
      order.facility_name
    );
    templateData.customer_id = order.customer_id;
    templateData.created_at = order.created_at?.toString();
    return PrintTemplateUtil.generateTemplate(templateData);
  }

  /**
   * Print the generated template
   */
  private printTemplate(htmlTemplate: string): void {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    printWindow.document.write(htmlTemplate);
    printWindow.document.close();
    printWindow.focus();
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };
  }

  /**
   * Print cart items
   */
  async printCart(
    cartItems: CartItem[],
    orderId: string,
    customerName?: string,
    paymentMethod: string = 'Cash',
    copyOfInvoice: boolean = false
  ): Promise<void> {
    const template = await this.generateCartPrintTemplate(cartItems, orderId, customerName, paymentMethod, copyOfInvoice);
    this.printTemplate(template);
  }

  /**
   * Print order
   */
  async printOrder(order: Order, copyOfInvoice: boolean = false): Promise<void> {
    const template = await this.generateOrderPrintTemplate(order, copyOfInvoice);
    this.printTemplate(template);
  }
}