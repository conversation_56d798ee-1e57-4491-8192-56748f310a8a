// Simple test to verify tax calculation logic
// This simulates the tax calculation for a sample cart item

// Sample cart item with tax rates
const sampleCartItem = {
  id: 'test-1',
  child_sku: 'TEST001',
  name: 'Test Product',
  selling_price: 100,
  quantity: 2,
  tax: 18, // Total tax rate
  cgst: 0,
  sgst: 0,
  igst: 18, // 18% IGST
  cess: 2   // 2% CESS
};

// Simulate the tax calculation logic
function calculateItemTax(item) {
  const itemTaxableAmount = item.selling_price * item.quantity;
  let itemTotalTax = 0;
  
  // Calculate IGST
  if (item.igst && item.igst > 0) {
    const igstAmount = (itemTaxableAmount * item.igst) / 100;
    itemTotalTax += igstAmount;
    console.log(`IGST: ${item.igst}% on ₹${itemTaxableAmount} = ₹${igstAmount}`);
  }
  
  // Add CESS
  if (item.cess && item.cess > 0) {
    const cessAmount = (itemTaxableAmount * item.cess) / 100;
    itemTotalTax += cessAmount;
    console.log(`CESS: ${item.cess}% on ₹${itemTaxableAmount} = ₹${cessAmount}`);
  }
  
  // Calculate CGST and SGST for display (IGST split in half)
  const cgstRate = item.igst / 2;
  const sgstRate = item.igst / 2;
  const cgstAmount = itemTaxableAmount * cgstRate / 100;
  const sgstAmount = itemTaxableAmount * sgstRate / 100;
  
  console.log(`\nFor display purposes:`);
  console.log(`CGST: ${cgstRate}% = ₹${cgstAmount}`);
  console.log(`SGST: ${sgstRate}% = ₹${sgstAmount}`);
  console.log(`Total Tax (IGST + CESS): ₹${itemTotalTax}`);
  console.log(`GST equals Total Tax: ₹${itemTotalTax}`);
  
  return {
    igstAmount: itemTaxableAmount * item.igst / 100,
    cessAmount: itemTaxableAmount * item.cess / 100,
    cgstAmount: cgstAmount,
    sgstAmount: sgstAmount,
    totalTax: itemTotalTax,
    totalWithTax: itemTaxableAmount + itemTotalTax
  };
}

// Test the calculation
console.log('=== Tax Calculation Test ===');
console.log(`Item: ${sampleCartItem.name}`);
console.log(`Price: ₹${sampleCartItem.selling_price} x ${sampleCartItem.quantity} = ₹${sampleCartItem.selling_price * sampleCartItem.quantity}`);
console.log('');

const result = calculateItemTax(sampleCartItem);

console.log('\n=== Expected Print Display ===');
console.log(`IGST: ${sampleCartItem.igst}% (₹${result.igstAmount}) [CGST: ${sampleCartItem.igst/2}% (₹${result.cgstAmount}), SGST: ${sampleCartItem.igst/2}% (₹${result.sgstAmount})]`);
console.log(`CESS: ${sampleCartItem.cess}% (₹${result.cessAmount})`);
console.log(`Total Tax: ₹${result.totalTax}`);
console.log(`Total with Tax: ₹${result.totalWithTax}`);
